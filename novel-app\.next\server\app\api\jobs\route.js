var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/jobs/route.js")
R.c("server/chunks/node_modules_next_b8125cc1._.js")
R.c("server/chunks/[root-of-the-server]__d8ddd92f._.js")
R.m("[project]/.next-internal/server/app/api/jobs/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/jobs/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/jobs/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
