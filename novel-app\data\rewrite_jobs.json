[{"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2, 3], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwd0brwg9jbziwin", "createdAt": "2025-09-23T09:35:13.052Z", "updatedAt": "2025-09-23T09:35:38.465Z", "result": "成功改写 3/3 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwkav1po6vf2u39jc", "createdAt": "2025-09-23T12:59:21.901Z", "updatedAt": "2025-09-23T12:59:24.350Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwkuenrx64sqx171q", "createdAt": "2025-09-23T13:14:33.783Z", "updatedAt": "2025-09-23T13:14:49.213Z", "result": "成功改写 2/2 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwl4437bqfb6k3g3ft", "createdAt": "2025-09-23T13:22:06.643Z", "updatedAt": "2025-09-23T13:22:36.920Z", "result": "成功改写 2/2 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfwl8mdqk3hp4w4ddzp", "createdAt": "2025-09-23T13:25:36.974Z", "updatedAt": "2025-09-23T13:25:52.881Z", "result": "成功改写 2/2 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxj9zhewme6f6vsk4", "createdAt": "2025-09-24T05:18:27.554Z", "updatedAt": "2025-09-24T05:18:34.342Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxjw1rnw2qr3lgdsfi", "createdAt": "2025-09-24T05:35:36.947Z", "updatedAt": "2025-09-24T05:35:56.120Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxkpt0uy1h8dneb7h", "createdAt": "2025-09-24T05:58:45.294Z", "updatedAt": "2025-09-24T05:59:19.046Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxku40q8w0t9qz5h05", "createdAt": "2025-09-24T06:02:06.170Z", "updatedAt": "2025-09-24T06:02:09.468Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxkx85qbasapiih92c", "createdAt": "2025-09-24T06:04:31.502Z", "updatedAt": "2025-09-24T06:04:37.318Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxkzi4m5p0es4a7d86", "createdAt": "2025-09-24T06:06:17.734Z", "updatedAt": "2025-09-24T06:06:22.895Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxl0l5xc0958393djh", "createdAt": "2025-09-24T06:07:08.325Z", "updatedAt": "2025-09-24T06:07:25.755Z", "result": "成功改写 1/1 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [1, 2, 3, 4, 5], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxl2ts8y4zgbzax21a", "createdAt": "2025-09-24T06:08:52.808Z", "updatedAt": "2025-09-24T06:09:26.452Z", "result": "成功改写 5/5 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfwcydxezrka3jsfl1", "chapters": [6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "completed", "progress": 100, "id": "mfxly8i8rxb8ko4lln", "createdAt": "2025-09-24T06:33:18.224Z", "updatedAt": "2025-09-24T06:36:09.406Z", "result": "成功改写 25/35 章节，结果保存在: C:\\Users\\<USER>\\Desktop\\cool\\in-the-novel\\novel-app\\data\\rewritten\\《神国之上》"}, {"novelId": "mfxrf7myydaku63lib", "chapters": [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40], "ruleId": "custom", "status": "failed", "progress": 13, "details": {"totalChapters": 40, "completedChapters": 5, "failedChapters": 0, "totalTokensUsed": 16214, "totalProcessingTime": 8783, "averageTimePerChapter": 1756.6, "apiKeyStats": [{"name": "My First Project", "requestCount": 4, "weight": 4, "isAvailable": true, "cooldownRemaining": 0}, {"name": "<PERSON><PERSON><PERSON>", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "Generative Language Client", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "In The Novel", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}, {"name": "chat", "requestCount": 0, "weight": 1, "isAvailable": true, "cooldownRemaining": 0}], "chapterResults": [{"chapterNumber": 1, "chapterTitle": "第一章 皇城的鬼", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4133, "processingTime": 7724, "completedAt": "2025-09-24T09:13:29.297Z"}, {"chapterNumber": 2, "chapterTitle": "第二章 醒来的少年", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 4356, "processingTime": 3196, "completedAt": "2025-09-24T09:13:24.774Z"}, null, {"chapterNumber": 4, "chapterTitle": "第四章 跪在殿前的少女", "success": true, "apiKeyUsed": "My First Project", "tokensUsed": 3797, "processingTime": 4567, "completedAt": "2025-09-24T09:13:30.353Z"}], "model": "gemini-2.5-flash-lite", "concurrency": 3}, "id": "mfxro2hsc2uoz2w2wfw", "createdAt": "2025-09-24T09:13:21.568Z", "updatedAt": "2025-09-24T09:13:30.354Z", "result": "改写失败: Cannot read properties of null (reading 'success')"}]