{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 67, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/database.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 数据类型定义\nexport interface Novel {\n  id: string;\n  title: string;\n  filename: string;\n  createdAt: string;\n  chapterCount?: number;\n}\n\nexport interface Chapter {\n  id: string;\n  novelId: string;\n  chapterNumber: number;\n  title: string;\n  content: string;\n  filename: string;\n  createdAt: string;\n}\n\nexport interface RewriteRule {\n  id: string;\n  name: string;\n  description: string;\n  rules: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface Character {\n  id: string;\n  novelId: string;\n  name: string;\n  role: string; // 角色类型：男主、女主、配角、反派、其他\n  description: string;\n  personality?: string; // 性格特点\n  appearance?: string; // 外貌描述\n  relationships?: string; // 人物关系\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface RewriteJob {\n  id: string;\n  novelId: string;\n  chapters: number[];\n  ruleId: string;\n  status: 'pending' | 'processing' | 'completed' | 'failed';\n  progress: number;\n  result?: string;\n  createdAt: string;\n  updatedAt: string;\n  // 新增详细信息字段\n  details?: {\n    totalChapters: number;\n    completedChapters: number;\n    failedChapters: number;\n    totalTokensUsed: number;\n    totalProcessingTime: number;\n    averageTimePerChapter: number;\n    apiKeyStats: Array<{\n      name: string;\n      requestCount: number;\n      weight: number;\n      isAvailable: boolean;\n    }>;\n    chapterResults: Array<{\n      chapterNumber: number;\n      chapterTitle: string;\n      success: boolean;\n      error?: string;\n      apiKeyUsed?: string;\n      tokensUsed?: number;\n      processingTime?: number;\n      completedAt?: string;\n    }>;\n    model?: string;\n    concurrency?: number;\n  };\n}\n\n// 数据存储路径\nconst DATA_DIR = path.join(process.cwd(), 'data');\nconst NOVELS_FILE = path.join(DATA_DIR, 'novels.json');\nconst CHAPTERS_FILE = path.join(DATA_DIR, 'chapters.json');\nconst RULES_FILE = path.join(DATA_DIR, 'rewrite_rules.json');\nconst JOBS_FILE = path.join(DATA_DIR, 'rewrite_jobs.json');\nconst CHARACTERS_FILE = path.join(DATA_DIR, 'characters.json');\n\n// 确保数据目录存在\nfunction ensureDataDir() {\n  if (!fs.existsSync(DATA_DIR)) {\n    fs.mkdirSync(DATA_DIR, { recursive: true });\n  }\n}\n\n// 读取JSON文件\nfunction readJsonFile<T>(filePath: string): T[] {\n  ensureDataDir();\n  if (!fs.existsSync(filePath)) {\n    return [];\n  }\n  try {\n    const data = fs.readFileSync(filePath, 'utf-8');\n    return JSON.parse(data);\n  } catch (error) {\n    console.error(`Error reading ${filePath}:`, error);\n    return [];\n  }\n}\n\n// 写入JSON文件\nfunction writeJsonFile<T>(filePath: string, data: T[]) {\n  ensureDataDir();\n  try {\n    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf-8');\n  } catch (error) {\n    console.error(`Error writing ${filePath}:`, error);\n    throw error;\n  }\n}\n\n// 生成唯一ID\nfunction generateId(): string {\n  return Date.now().toString(36) + Math.random().toString(36).substr(2);\n}\n\n// 小说相关操作\nexport const novelDb = {\n  getAll: (): Novel[] => readJsonFile<Novel>(NOVELS_FILE),\n\n  getById: (id: string): Novel | undefined => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    return novels.find(novel => novel.id === id);\n  },\n\n  create: (novel: Omit<Novel, 'id' | 'createdAt'>): Novel => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const newNovel: Novel = {\n      ...novel,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    novels.push(newNovel);\n    writeJsonFile(NOVELS_FILE, novels);\n    return newNovel;\n  },\n\n  update: (id: string, updates: Partial<Novel>): Novel | null => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return null;\n\n    novels[index] = { ...novels[index], ...updates };\n    writeJsonFile(NOVELS_FILE, novels);\n    return novels[index];\n  },\n\n  delete: (id: string): boolean => {\n    const novels = readJsonFile<Novel>(NOVELS_FILE);\n    const index = novels.findIndex(novel => novel.id === id);\n    if (index === -1) return false;\n\n    novels.splice(index, 1);\n    writeJsonFile(NOVELS_FILE, novels);\n    return true;\n  }\n};\n\n// 章节相关操作\nexport const chapterDb = {\n  getAll: (): Chapter[] => readJsonFile<Chapter>(CHAPTERS_FILE),\n\n  getByNovelId: (novelId: string): Chapter[] => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.filter(chapter => chapter.novelId === novelId);\n  },\n\n  getById: (id: string): Chapter | undefined => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    return chapters.find(chapter => chapter.id === id);\n  },\n\n  create: (chapter: Omit<Chapter, 'id' | 'createdAt'>): Chapter => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapter: Chapter = {\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    };\n    chapters.push(newChapter);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return newChapter;\n  },\n\n  createBatch: (chapters: Omit<Chapter, 'id' | 'createdAt'>[]): Chapter[] => {\n    const existingChapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const newChapters = chapters.map(chapter => ({\n      ...chapter,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n    }));\n    existingChapters.push(...newChapters);\n    writeJsonFile(CHAPTERS_FILE, existingChapters);\n    return newChapters;\n  },\n\n  delete: (id: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const index = chapters.findIndex(chapter => chapter.id === id);\n    if (index === -1) return false;\n\n    chapters.splice(index, 1);\n    writeJsonFile(CHAPTERS_FILE, chapters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const chapters = readJsonFile<Chapter>(CHAPTERS_FILE);\n    const filteredChapters = chapters.filter(chapter => chapter.novelId !== novelId);\n    writeJsonFile(CHAPTERS_FILE, filteredChapters);\n    return true;\n  }\n};\n\n// 改写规则相关操作\nexport const ruleDb = {\n  getAll: (): RewriteRule[] => readJsonFile<RewriteRule>(RULES_FILE),\n\n  getById: (id: string): RewriteRule | undefined => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    return rules.find(rule => rule.id === id);\n  },\n\n  create: (rule: Omit<RewriteRule, 'id' | 'createdAt' | 'updatedAt'>): RewriteRule => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const newRule: RewriteRule = {\n      ...rule,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    rules.push(newRule);\n    writeJsonFile(RULES_FILE, rules);\n    return newRule;\n  },\n\n  update: (id: string, updates: Partial<RewriteRule>): RewriteRule | null => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return null;\n\n    rules[index] = {\n      ...rules[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(RULES_FILE, rules);\n    return rules[index];\n  },\n\n  delete: (id: string): boolean => {\n    const rules = readJsonFile<RewriteRule>(RULES_FILE);\n    const index = rules.findIndex(rule => rule.id === id);\n    if (index === -1) return false;\n\n    rules.splice(index, 1);\n    writeJsonFile(RULES_FILE, rules);\n    return true;\n  }\n};\n\n// 改写任务相关操作\nexport const jobDb = {\n  getAll: (): RewriteJob[] => readJsonFile<RewriteJob>(JOBS_FILE),\n\n  getById: (id: string): RewriteJob | undefined => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    return jobs.find(job => job.id === id);\n  },\n\n  create: (job: Omit<RewriteJob, 'id' | 'createdAt' | 'updatedAt'>): RewriteJob => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const newJob: RewriteJob = {\n      ...job,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    jobs.push(newJob);\n    writeJsonFile(JOBS_FILE, jobs);\n    return newJob;\n  },\n\n  update: (id: string, updates: Partial<RewriteJob>): RewriteJob | null => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return null;\n\n    jobs[index] = {\n      ...jobs[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(JOBS_FILE, jobs);\n    return jobs[index];\n  },\n\n  delete: (id: string): boolean => {\n    const jobs = readJsonFile<RewriteJob>(JOBS_FILE);\n    const index = jobs.findIndex(job => job.id === id);\n    if (index === -1) return false;\n\n    jobs.splice(index, 1);\n    writeJsonFile(JOBS_FILE, jobs);\n    return true;\n  }\n};\n\n// 人物设定相关操作\nexport const characterDb = {\n  getAll: (): Character[] => readJsonFile<Character>(CHARACTERS_FILE),\n\n  getByNovelId: (novelId: string): Character[] => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.filter(character => character.novelId === novelId);\n  },\n\n  getById: (id: string): Character | undefined => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    return characters.find(character => character.id === id);\n  },\n\n  create: (character: Omit<Character, 'id' | 'createdAt' | 'updatedAt'>): Character => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const newCharacter: Character = {\n      ...character,\n      id: generateId(),\n      createdAt: new Date().toISOString(),\n      updatedAt: new Date().toISOString(),\n    };\n    characters.push(newCharacter);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return newCharacter;\n  },\n\n  update: (id: string, updates: Partial<Character>): Character | null => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return null;\n\n    characters[index] = {\n      ...characters[index],\n      ...updates,\n      updatedAt: new Date().toISOString()\n    };\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return characters[index];\n  },\n\n  delete: (id: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const index = characters.findIndex(character => character.id === id);\n    if (index === -1) return false;\n\n    characters.splice(index, 1);\n    writeJsonFile(CHARACTERS_FILE, characters);\n    return true;\n  },\n\n  deleteByNovelId: (novelId: string): boolean => {\n    const characters = readJsonFile<Character>(CHARACTERS_FILE);\n    const filteredCharacters = characters.filter(character => character.novelId !== novelId);\n    writeJsonFile(CHARACTERS_FILE, filteredCharacters);\n    return true;\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAkFA,SAAS;AACT,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;AAC1C,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU;AACxC,MAAM,gBAAgB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAC1C,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,UAAU;AACvC,MAAM,YAAY,4GAAI,CAAC,IAAI,CAAC,UAAU;AACtC,MAAM,kBAAkB,4GAAI,CAAC,IAAI,CAAC,UAAU;AAE5C,WAAW;AACX,SAAS;IACP,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,wGAAE,CAAC,SAAS,CAAC,UAAU;YAAE,WAAW;QAAK;IAC3C;AACF;AAEA,WAAW;AACX,SAAS,aAAgB,QAAgB;IACvC;IACA,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,WAAW;QAC5B,OAAO,EAAE;IACX;IACA,IAAI;QACF,MAAM,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACvC,OAAO,KAAK,KAAK,CAAC;IACpB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,OAAO,EAAE;IACX;AACF;AAEA,WAAW;AACX,SAAS,cAAiB,QAAgB,EAAE,IAAS;IACnD;IACA,IAAI;QACF,wGAAE,CAAC,aAAa,CAAC,UAAU,KAAK,SAAS,CAAC,MAAM,MAAM,IAAI;IAC5D,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC,EAAE;QAC5C,MAAM;IACR;AACF;AAEA,SAAS;AACT,SAAS;IACP,OAAO,KAAK,GAAG,GAAG,QAAQ,CAAC,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC;AACrE;AAGO,MAAM,UAAU;IACrB,QAAQ,IAAe,aAAoB;IAE3C,SAAS,CAAC;QACR,MAAM,SAAS,aAAoB;QACnC,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IAC3C;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,WAAkB;YACtB,GAAG,KAAK;YACR,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,OAAO,IAAI,CAAC;QACZ,cAAc,aAAa;QAC3B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,CAAC,MAAM,GAAG;YAAE,GAAG,MAAM,CAAC,MAAM;YAAE,GAAG,OAAO;QAAC;QAC/C,cAAc,aAAa;QAC3B,OAAO,MAAM,CAAC,MAAM;IACtB;IAEA,QAAQ,CAAC;QACP,MAAM,SAAS,aAAoB;QACnC,MAAM,QAAQ,OAAO,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;QACrD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,OAAO,MAAM,CAAC,OAAO;QACrB,cAAc,aAAa;QAC3B,OAAO;IACT;AACF;AAGO,MAAM,YAAY;IACvB,QAAQ,IAAiB,aAAsB;IAE/C,cAAc,CAAC;QACb,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;IACxD;IAEA,SAAS,CAAC;QACR,MAAM,WAAW,aAAsB;QACvC,OAAO,SAAS,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;IACjD;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,aAAsB;YAC1B,GAAG,OAAO;YACV,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,SAAS,IAAI,CAAC;QACd,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,mBAAmB,aAAsB;QAC/C,MAAM,cAAc,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC3C,GAAG,OAAO;gBACV,IAAI;gBACJ,WAAW,IAAI,OAAO,WAAW;YACnC,CAAC;QACD,iBAAiB,IAAI,IAAI;QACzB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,QAAQ,CAAC;QACP,MAAM,WAAW,aAAsB;QACvC,MAAM,QAAQ,SAAS,SAAS,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,SAAS,MAAM,CAAC,OAAO;QACvB,cAAc,eAAe;QAC7B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,WAAW,aAAsB;QACvC,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO,KAAK;QACxE,cAAc,eAAe;QAC7B,OAAO;IACT;AACF;AAGO,MAAM,SAAS;IACpB,QAAQ,IAAqB,aAA0B;IAEvD,SAAS,CAAC;QACR,MAAM,QAAQ,aAA0B;QACxC,OAAO,MAAM,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IACxC;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,UAAuB;YAC3B,GAAG,IAAI;YACP,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,MAAM,IAAI,CAAC;QACX,cAAc,YAAY;QAC1B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,CAAC,MAAM,GAAG;YACb,GAAG,KAAK,CAAC,MAAM;YACf,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,YAAY;QAC1B,OAAO,KAAK,CAAC,MAAM;IACrB;IAEA,QAAQ,CAAC;QACP,MAAM,QAAQ,aAA0B;QACxC,MAAM,QAAQ,MAAM,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClD,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,MAAM,MAAM,CAAC,OAAO;QACpB,cAAc,YAAY;QAC1B,OAAO;IACT;AACF;AAGO,MAAM,QAAQ;IACnB,QAAQ,IAAoB,aAAyB;IAErD,SAAS,CAAC;QACR,MAAM,OAAO,aAAyB;QACtC,OAAO,KAAK,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;IACrC;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,SAAqB;YACzB,GAAG,GAAG;YACN,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,KAAK,IAAI,CAAC;QACV,cAAc,WAAW;QACzB,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,IAAI,CAAC,MAAM,GAAG;YACZ,GAAG,IAAI,CAAC,MAAM;YACd,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,WAAW;QACzB,OAAO,IAAI,CAAC,MAAM;IACpB;IAEA,QAAQ,CAAC;QACP,MAAM,OAAO,aAAyB;QACtC,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;QAC/C,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,KAAK,MAAM,CAAC,OAAO;QACnB,cAAc,WAAW;QACzB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,QAAQ,IAAmB,aAAwB;IAEnD,cAAc,CAAC;QACb,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;IAC9D;IAEA,SAAS,CAAC;QACR,MAAM,aAAa,aAAwB;QAC3C,OAAO,WAAW,IAAI,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;IACvD;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,eAA0B;YAC9B,GAAG,SAAS;YACZ,IAAI;YACJ,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,WAAW,IAAI,CAAC;QAChB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,QAAQ,CAAC,IAAY;QACnB,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,UAAU,CAAC,MAAM,GAAG;YAClB,GAAG,UAAU,CAAC,MAAM;YACpB,GAAG,OAAO;YACV,WAAW,IAAI,OAAO,WAAW;QACnC;QACA,cAAc,iBAAiB;QAC/B,OAAO,UAAU,CAAC,MAAM;IAC1B;IAEA,QAAQ,CAAC;QACP,MAAM,aAAa,aAAwB;QAC3C,MAAM,QAAQ,WAAW,SAAS,CAAC,CAAA,YAAa,UAAU,EAAE,KAAK;QACjE,IAAI,UAAU,CAAC,GAAG,OAAO;QAEzB,WAAW,MAAM,CAAC,OAAO;QACzB,cAAc,iBAAiB;QAC/B,OAAO;IACT;IAEA,iBAAiB,CAAC;QAChB,MAAM,aAAa,aAAwB;QAC3C,MAAM,qBAAqB,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,OAAO,KAAK;QAChF,cAAc,iBAAiB;QAC/B,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/gemini.ts"], "sourcesContent": ["// Gemini API 集成 - 多Key池管理\n// API Keys配置 - 第一个key是其他4个的4倍强度\nconst API_KEYS = [\n  {\n    key: 'AIzaSyA0-QhTg39yuxXdwrVOKmBtOQ0suNhuYnw', // My First Project - 4倍强度\n    name: 'My First Project',\n    weight: 4, // 权重，表示相对强度\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0, // 冷却时间\n  },\n  {\n    key: 'AIzaSyDI3XaHH7pC9NUYiuekHEuYwrhwJpcu47Y', // ankibot\n    name: 'ankibot',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyC9myOYteSKsDcXVO-dVYbiMZNw4v0BZhY', // Generative Language Client\n    name: 'Generative Language Client',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyCY6TO1xBNh1f_vmmBo_H_icOxHkO3YgNc', // In The Novel\n    name: 'In The Novel',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  },\n  {\n    key: 'AIzaSyA43ZQq4VF0X9bzUqDAlbka8wD4tasdLXk', // chat\n    name: 'chat',\n    weight: 1,\n    requestCount: 0,\n    lastUsed: 0,\n    cooldownUntil: 0,\n  }\n];\n\n// API配置\nconst GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-lite:generateContent';\nconst REQUEST_DELAY = 1000; // 请求间隔（毫秒）\nconst COOLDOWN_DURATION = 60000; // 429错误后的冷却时间（毫秒）\nconst MAX_RETRIES = 3; // 最大重试次数\n\n// API Key管理类\nclass ApiKeyManager {\n  private keys = [...API_KEYS];\n\n  // 获取最佳可用的API Key\n  getBestAvailableKey() {\n    const now = Date.now();\n\n    // 过滤掉冷却中的key\n    const availableKeys = this.keys.filter(key => key.cooldownUntil <= now);\n\n    if (availableKeys.length === 0) {\n      // 如果所有key都在冷却中，返回冷却时间最短的\n      return this.keys.reduce((min, key) =>\n        key.cooldownUntil < min.cooldownUntil ? key : min\n      );\n    }\n\n    // 根据权重和使用频率选择最佳key\n    const bestKey = availableKeys.reduce((best, key) => {\n      const keyScore = key.weight / (key.requestCount + 1);\n      const bestScore = best.weight / (best.requestCount + 1);\n      return keyScore > bestScore ? key : best;\n    });\n\n    return bestKey;\n  }\n\n  // 记录API使用\n  recordUsage(keyName: string, success: boolean) {\n    const key = this.keys.find(k => k.name === keyName);\n    if (key) {\n      key.requestCount++;\n      key.lastUsed = Date.now();\n\n      if (!success) {\n        // 如果失败，设置冷却时间\n        key.cooldownUntil = Date.now() + COOLDOWN_DURATION;\n      }\n    }\n  }\n\n  // 获取统计信息\n  getStats() {\n    return this.keys.map(key => ({\n      name: key.name,\n      requestCount: key.requestCount,\n      weight: key.weight,\n      isAvailable: key.cooldownUntil <= Date.now(),\n      cooldownRemaining: Math.max(0, key.cooldownUntil - Date.now()),\n    }));\n  }\n}\n\nconst keyManager = new ApiKeyManager();\n\nexport interface RewriteRequest {\n  originalText: string;\n  rules: string;\n  chapterTitle?: string;\n  chapterNumber?: number;\n}\n\nexport interface RewriteResponse {\n  rewrittenText: string;\n  success: boolean;\n  error?: string;\n  apiKeyUsed?: string;\n  tokensUsed?: number;\n  model?: string;\n  processingTime?: number;\n}\n\n// 构建改写提示词\nfunction buildPrompt(request: RewriteRequest): string {\n  const { originalText, rules, chapterTitle, chapterNumber } = request;\n\n  return `你是一个专业的小说改写助手。请根据以下规则对小说章节进行改写：\n\n改写规则：\n${rules}\n\n${chapterTitle ? `${chapterTitle}` : ''}\n// ${chapterNumber ? `章节编号：第${chapterNumber}章` : ''}\n\n原文内容：\n${originalText}\n\n请严格按照改写规则进行改写，保持故事的连贯性和可读性。改写后的内容应该：\n1. 遵循所有指定的改写规则\n2. 保持原文的基本故事框架（除非规则要求修改）\n3. 确保文字流畅自然\n4. 保持角色的基本性格特征（除非规则要求修改）\n\n请直接输出改写后的内容，不要添加任何解释或说明：`;\n}\n\n// 调用Gemini API进行文本改写 - 支持多Key和重试\nexport async function rewriteText(request: RewriteRequest): Promise<RewriteResponse> {\n  const startTime = Date.now();\n  let lastError = '';\n\n  for (let attempt = 0; attempt < MAX_RETRIES; attempt++) {\n    try {\n      const apiKey = keyManager.getBestAvailableKey();\n\n      // 如果key在冷却中，等待一段时间\n      if (apiKey.cooldownUntil > Date.now()) {\n        const waitTime = Math.min(apiKey.cooldownUntil - Date.now(), 5000); // 最多等待5秒\n        await new Promise(resolve => setTimeout(resolve, waitTime));\n      }\n\n      const prompt = buildPrompt(request);\n\n      const response = await fetch(`${GEMINI_API_URL}?key=${apiKey.key}`, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          contents: [{\n            parts: [{\n              text: prompt\n            }]\n          }],\n          generationConfig: {\n            temperature: 0.7,\n            topK: 40,\n            topP: 0.95,\n            maxOutputTokens: 8192,\n          },\n          safetySettings: [\n            {\n              category: \"HARM_CATEGORY_HARASSMENT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_HATE_SPEECH\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_SEXUALLY_EXPLICIT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            },\n            {\n              category: \"HARM_CATEGORY_DANGEROUS_CONTENT\",\n              threshold: \"BLOCK_MEDIUM_AND_ABOVE\"\n            }\n          ]\n        }),\n      });\n\n      const processingTime = Date.now() - startTime;\n\n      if (response.status === 429) {\n        // 429错误，记录失败并尝试下一个key\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API限流 (${apiKey.name})`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));\n          continue;\n        }\n      }\n\n      if (!response.ok) {\n        const errorData = await response.text();\n        console.error('Gemini API error:', errorData);\n        keyManager.recordUsage(apiKey.name, false);\n        lastError = `API请求失败: ${response.status} ${response.statusText}`;\n\n        if (attempt < MAX_RETRIES - 1) {\n          await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n          continue;\n        }\n\n        return {\n          rewrittenText: '',\n          success: false,\n          error: lastError,\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const data = await response.json();\n\n      // 记录成功使用\n      keyManager.recordUsage(apiKey.name, true);\n\n      if (!data.candidates || data.candidates.length === 0) {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '没有收到有效的响应内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const candidate = data.candidates[0];\n\n      if (candidate.finishReason === 'SAFETY') {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '内容被安全过滤器拦截，请调整改写规则或原文内容',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      if (!candidate.content || !candidate.content.parts || candidate.content.parts.length === 0) {\n        return {\n          rewrittenText: '',\n          success: false,\n          error: '响应内容格式错误',\n          apiKeyUsed: apiKey.name,\n          processingTime,\n        };\n      }\n\n      const rewrittenText = candidate.content.parts[0].text;\n\n      // 尝试从响应中提取token使用信息\n      const tokensUsed = data.usageMetadata?.totalTokenCount || 0;\n\n      return {\n        rewrittenText: rewrittenText.trim(),\n        success: true,\n        apiKeyUsed: apiKey.name,\n        tokensUsed,\n        model: 'gemini-2.5-flash-lite',\n        processingTime,\n      };\n\n    } catch (error) {\n      console.error('Gemini API调用错误:', error);\n      lastError = `网络错误: ${error instanceof Error ? error.message : '未知错误'}`;\n\n      if (attempt < MAX_RETRIES - 1) {\n        await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY * (attempt + 1)));\n      }\n    }\n  }\n\n  return {\n    rewrittenText: '',\n    success: false,\n    error: `重试${MAX_RETRIES}次后仍然失败: ${lastError}`,\n    processingTime: Date.now() - startTime,\n  };\n}\n\n// 改进的批量改写函数 - 支持实时写入和详细进度跟踪\nexport async function rewriteChapters(\n  chapters: Array<{ content: string; title: string; number: number }>,\n  rules: string,\n  onProgress?: (progress: number, currentChapter: number, details?: any) => void,\n  onChapterComplete?: (chapterIndex: number, result: any) => void,\n  concurrency: number = 3 // 降低并发数以避免429错误\n): Promise<Array<{ success: boolean; content: string; error?: string; details?: any }>> {\n  const results: Array<{ success: boolean; content: string; error?: string; details?: any }> = new Array(chapters.length);\n  let completed = 0;\n  let totalTokensUsed = 0;\n  const startTime = Date.now();\n\n  // 使用更保守的并发策略\n  const semaphore = new Semaphore(concurrency);\n\n  const processChapter = async (chapter: { content: string; title: string; number: number }, index: number) => {\n    await semaphore.acquire();\n    const chapterStartTime = Date.now();\n\n    try {\n\n      const result = await rewriteText({\n        originalText: chapter.content,\n        rules,\n        chapterTitle: chapter.title,\n        chapterNumber: chapter.number,\n      });\n\n      const chapterProcessingTime = Date.now() - chapterStartTime;\n\n      if (result.tokensUsed) {\n        totalTokensUsed += result.tokensUsed;\n      }\n\n      const chapterResult = {\n        success: result.success,\n        content: result.rewrittenText,\n        error: result.error,\n        details: {\n          apiKeyUsed: result.apiKeyUsed,\n          tokensUsed: result.tokensUsed,\n          model: result.model,\n          processingTime: chapterProcessingTime,\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n        }\n      };\n\n      results[index] = chapterResult;\n      completed++;\n\n      // 实时回调章节完成\n      if (onChapterComplete) {\n        onChapterComplete(index, chapterResult);\n      }\n\n      // 更新进度，包含详细信息\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            processingTime: chapterProcessingTime,\n            apiKey: result.apiKeyUsed,\n            tokens: result.tokensUsed,\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      // 添加请求间隔\n      await new Promise(resolve => setTimeout(resolve, REQUEST_DELAY));\n\n      return result;\n    } catch (error) {\n      const chapterErrorTime = Date.now();\n      const errorResult = {\n        success: false,\n        content: '',\n        error: `处理失败: ${error instanceof Error ? error.message : '未知错误'}`,\n        details: {\n          chapterNumber: chapter.number,\n          chapterTitle: chapter.title,\n          processingTime: chapterErrorTime - chapterStartTime,\n        }\n      };\n\n      results[index] = errorResult;\n      completed++;\n\n      if (onChapterComplete) {\n        onChapterComplete(index, errorResult);\n      }\n\n      if (onProgress) {\n        const progressDetails = {\n          completed,\n          total: chapters.length,\n          totalTokensUsed,\n          totalTime: Date.now() - startTime,\n          averageTimePerChapter: (Date.now() - startTime) / completed,\n          apiKeyStats: keyManager.getStats(),\n          currentChapter: {\n            number: chapter.number,\n            title: chapter.title,\n            error: error instanceof Error ? error.message : '未知错误',\n          }\n        };\n\n        onProgress((completed / chapters.length) * 100, chapter.number, progressDetails);\n      }\n\n      return null;\n    } finally {\n      semaphore.release();\n    }\n  };\n\n  // 并发处理所有章节\n  const promises = chapters.map((chapter, index) => processChapter(chapter, index));\n  await Promise.all(promises);\n\n  return results;\n}\n\n// 信号量类，用于控制并发\nclass Semaphore {\n  private permits: number;\n  private waitQueue: Array<() => void> = [];\n\n  constructor(permits: number) {\n    this.permits = permits;\n  }\n\n  async acquire(): Promise<void> {\n    if (this.permits > 0) {\n      this.permits--;\n      return Promise.resolve();\n    }\n\n    return new Promise<void>((resolve) => {\n      this.waitQueue.push(resolve);\n    });\n  }\n\n  release(): void {\n    this.permits++;\n    if (this.waitQueue.length > 0) {\n      const resolve = this.waitQueue.shift();\n      if (resolve) {\n        this.permits--;\n        resolve();\n      }\n    }\n  }\n}\n\n// 测试API连接 - 增强版\nexport async function testGeminiConnection(): Promise<{\n  success: boolean;\n  error?: string;\n  details?: any;\n}> {\n  try {\n    const testResult = await rewriteText({\n      originalText: '这是一个测试文本。',\n      rules: '保持原文不变',\n    });\n\n    return {\n      success: testResult.success,\n      error: testResult.error,\n      details: {\n        apiKeyUsed: testResult.apiKeyUsed,\n        tokensUsed: testResult.tokensUsed,\n        model: testResult.model,\n        processingTime: testResult.processingTime,\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  } catch (error) {\n    return {\n      success: false,\n      error: `连接测试失败: ${error instanceof Error ? error.message : '未知错误'}`,\n      details: {\n        apiKeyStats: keyManager.getStats(),\n      }\n    };\n  }\n}\n\n// 获取API Key使用统计\nexport function getApiKeyStats() {\n  return keyManager.getStats();\n}\n\n// 重置API Key统计\nexport function resetApiKeyStats() {\n  API_KEYS.forEach(key => {\n    key.requestCount = 0;\n    key.lastUsed = 0;\n    key.cooldownUntil = 0;\n  });\n}\n\n// 预设的改写规则模板\nexport let PRESET_RULES = {\n  romance_focus: {\n    name: '感情戏增强',\n    description: '扩写男女主互动内容，对非感情戏部分一笔带过',\n    rules: `请按照以下规则改写：\n1. 大幅扩写男女主角之间的互动情节，增加对话、心理描写和情感细节\n2. 对战斗、修炼、政治等非感情戏情节进行简化，一笔带过\n3. 增加角色间的情感张力和暧昧氛围\n4. 保持故事主线不变，但重点突出感情发展`\n  },\n\n  character_fix: {\n    name: '人设修正',\n    description: '修正主角人设和对话风格',\n    rules: `请按照以下规则改写：\n1. 修正主角的性格设定，使其更加立体和讨喜\n2. 改善对话风格，使其更加自然流畅\n3. 去除过于中二或不合理的行为描写\n4. 保持角色的核心特征，但优化表现方式`\n  },\n\n  toxic_content_removal: {\n    name: '毒点清除',\n    description: '移除送女、绿帽等毒点情节',\n    rules: `请按照以下规则改写：\n1. 完全移除或修改送女、绿帽、圣母等毒点情节\n2. 删除或改写让读者不适的桥段\n3. 保持故事逻辑的完整性\n4. 用更合理的情节替代被删除的内容`\n  },\n\n  pacing_improvement: {\n    name: '节奏优化',\n    description: '优化故事节奏，删除拖沓内容',\n    rules: `请按照以下规则改写：\n1. 删除重复和拖沓的描写\n2. 加快故事节奏，突出重点情节\n3. 简化过于冗长的对话和心理描写\n4. 保持故事的紧凑性和可读性`\n  },\n\n  custom: {\n    name: '自定义规则',\n    description: '用户自定义的改写规则',\n    rules: ''\n  }\n};\n\n// 添加自定义预设规则\nexport function addCustomPreset(name: string, description: string, rules: string): string {\n  const key = `custom_${Date.now()}`;\n  PRESET_RULES = {\n    ...PRESET_RULES,\n    [key]: {\n      name,\n      description,\n      rules\n    }\n  };\n  return key;\n}\n"], "names": [], "mappings": "AAAA,0BAA0B;AAC1B,gCAAgC;;;;;;;;;;;;;;;;;AAChC,MAAM,WAAW;IACf;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;IACA;QACE,KAAK;QACL,MAAM;QACN,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe;IACjB;CACD;AAED,QAAQ;AACR,MAAM,iBAAiB;AACvB,MAAM,gBAAgB,MAAM,WAAW;AACvC,MAAM,oBAAoB,OAAO,kBAAkB;AACnD,MAAM,cAAc,GAAG,SAAS;AAEhC,aAAa;AACb,MAAM;IACI,OAAO;WAAI;KAAS,CAAC;IAE7B,iBAAiB;IACjB,sBAAsB;QACpB,MAAM,MAAM,KAAK,GAAG;QAEpB,aAAa;QACb,MAAM,gBAAgB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,aAAa,IAAI;QAEnE,IAAI,cAAc,MAAM,KAAK,GAAG;YAC9B,yBAAyB;YACzB,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,KAAK,MAC5B,IAAI,aAAa,GAAG,IAAI,aAAa,GAAG,MAAM;QAElD;QAEA,mBAAmB;QACnB,MAAM,UAAU,cAAc,MAAM,CAAC,CAAC,MAAM;YAC1C,MAAM,WAAW,IAAI,MAAM,GAAG,CAAC,IAAI,YAAY,GAAG,CAAC;YACnD,MAAM,YAAY,KAAK,MAAM,GAAG,CAAC,KAAK,YAAY,GAAG,CAAC;YACtD,OAAO,WAAW,YAAY,MAAM;QACtC;QAEA,OAAO;IACT;IAEA,UAAU;IACV,YAAY,OAAe,EAAE,OAAgB,EAAE;QAC7C,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;QAC3C,IAAI,KAAK;YACP,IAAI,YAAY;YAChB,IAAI,QAAQ,GAAG,KAAK,GAAG;YAEvB,IAAI,CAAC,SAAS;gBACZ,cAAc;gBACd,IAAI,aAAa,GAAG,KAAK,GAAG,KAAK;YACnC;QACF;IACF;IAEA,SAAS;IACT,WAAW;QACT,OAAO,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;gBAC3B,MAAM,IAAI,IAAI;gBACd,cAAc,IAAI,YAAY;gBAC9B,QAAQ,IAAI,MAAM;gBAClB,aAAa,IAAI,aAAa,IAAI,KAAK,GAAG;gBAC1C,mBAAmB,KAAK,GAAG,CAAC,GAAG,IAAI,aAAa,GAAG,KAAK,GAAG;YAC7D,CAAC;IACH;AACF;AAEA,MAAM,aAAa,IAAI;AAmBvB,UAAU;AACV,SAAS,YAAY,OAAuB;IAC1C,MAAM,EAAE,YAAY,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa,EAAE,GAAG;IAE7D,OAAO,CAAC;;;AAGV,EAAE,MAAM;;AAER,EAAE,eAAe,GAAG,cAAc,GAAG,GAAG;GACrC,EAAE,gBAAgB,CAAC,MAAM,EAAE,cAAc,CAAC,CAAC,GAAG,GAAG;;;AAGpD,EAAE,aAAa;;;;;;;;wBAQS,CAAC;AACzB;AAGO,eAAe,YAAY,OAAuB;IACvD,MAAM,YAAY,KAAK,GAAG;IAC1B,IAAI,YAAY;IAEhB,IAAK,IAAI,UAAU,GAAG,UAAU,aAAa,UAAW;QACtD,IAAI;YACF,MAAM,SAAS,WAAW,mBAAmB;YAE7C,mBAAmB;YACnB,IAAI,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI;gBACrC,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,aAAa,GAAG,KAAK,GAAG,IAAI,OAAO,SAAS;gBAC7E,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YAEA,MAAM,SAAS,YAAY;YAE3B,MAAM,WAAW,MAAM,MAAM,GAAG,eAAe,KAAK,EAAE,OAAO,GAAG,EAAE,EAAE;gBAClE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,UAAU;wBAAC;4BACT,OAAO;gCAAC;oCACN,MAAM;gCACR;6BAAE;wBACJ;qBAAE;oBACF,kBAAkB;wBAChB,aAAa;wBACb,MAAM;wBACN,MAAM;wBACN,iBAAiB;oBACnB;oBACA,gBAAgB;wBACd;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;wBACA;4BACE,UAAU;4BACV,WAAW;wBACb;qBACD;gBACH;YACF;YAEA,MAAM,iBAAiB,KAAK,GAAG,KAAK;YAEpC,IAAI,SAAS,MAAM,KAAK,KAAK;gBAC3B,sBAAsB;gBACtB,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,CAAC,OAAO,EAAE,OAAO,IAAI,CAAC,CAAC,CAAC;gBAEpC,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,gBAAgB,CAAC,UAAU,CAAC;oBAC7E;gBACF;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;gBACpC,YAAY,CAAC,SAAS,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;gBAEhE,IAAI,UAAU,cAAc,GAAG;oBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;oBACjD;gBACF;gBAEA,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,SAAS;YACT,WAAW,WAAW,CAAC,OAAO,IAAI,EAAE;YAEpC,IAAI,CAAC,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,MAAM,KAAK,GAAG;gBACpD,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;YAEpC,IAAI,UAAU,YAAY,KAAK,UAAU;gBACvC,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,KAAK,IAAI,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC1F,OAAO;oBACL,eAAe;oBACf,SAAS;oBACT,OAAO;oBACP,YAAY,OAAO,IAAI;oBACvB;gBACF;YACF;YAEA,MAAM,gBAAgB,UAAU,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAAI;YAErD,oBAAoB;YACpB,MAAM,aAAa,KAAK,aAAa,EAAE,mBAAmB;YAE1D,OAAO;gBACL,eAAe,cAAc,IAAI;gBACjC,SAAS;gBACT,YAAY,OAAO,IAAI;gBACvB;gBACA,OAAO;gBACP;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,YAAY,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YAEtE,IAAI,UAAU,cAAc,GAAG;gBAC7B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS,gBAAgB,CAAC,UAAU,CAAC;YAC/E;QACF;IACF;IAEA,OAAO;QACL,eAAe;QACf,SAAS;QACT,OAAO,CAAC,EAAE,EAAE,YAAY,QAAQ,EAAE,WAAW;QAC7C,gBAAgB,KAAK,GAAG,KAAK;IAC/B;AACF;AAGO,eAAe,gBACpB,QAAmE,EACnE,KAAa,EACb,UAA8E,EAC9E,iBAA+D,EAC/D,cAAsB,EAAE,gBAAgB;AAAjB;IAEvB,MAAM,UAAuF,IAAI,MAAM,SAAS,MAAM;IACtH,IAAI,YAAY;IAChB,IAAI,kBAAkB;IACtB,MAAM,YAAY,KAAK,GAAG;IAE1B,aAAa;IACb,MAAM,YAAY,IAAI,UAAU;IAEhC,MAAM,iBAAiB,OAAO,SAA6D;QACzF,MAAM,UAAU,OAAO;QACvB,MAAM,mBAAmB,KAAK,GAAG;QAEjC,IAAI;YAEF,MAAM,SAAS,MAAM,YAAY;gBAC/B,cAAc,QAAQ,OAAO;gBAC7B;gBACA,cAAc,QAAQ,KAAK;gBAC3B,eAAe,QAAQ,MAAM;YAC/B;YAEA,MAAM,wBAAwB,KAAK,GAAG,KAAK;YAE3C,IAAI,OAAO,UAAU,EAAE;gBACrB,mBAAmB,OAAO,UAAU;YACtC;YAEA,MAAM,gBAAgB;gBACpB,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,aAAa;gBAC7B,OAAO,OAAO,KAAK;gBACnB,SAAS;oBACP,YAAY,OAAO,UAAU;oBAC7B,YAAY,OAAO,UAAU;oBAC7B,OAAO,OAAO,KAAK;oBACnB,gBAAgB;oBAChB,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;gBAC7B;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,WAAW;YACX,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,cAAc;YACd,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,gBAAgB;wBAChB,QAAQ,OAAO,UAAU;wBACzB,QAAQ,OAAO,UAAU;oBAC3B;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,SAAS;YACT,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM,mBAAmB,KAAK,GAAG;YACjC,MAAM,cAAc;gBAClB,SAAS;gBACT,SAAS;gBACT,OAAO,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;gBACjE,SAAS;oBACP,eAAe,QAAQ,MAAM;oBAC7B,cAAc,QAAQ,KAAK;oBAC3B,gBAAgB,mBAAmB;gBACrC;YACF;YAEA,OAAO,CAAC,MAAM,GAAG;YACjB;YAEA,IAAI,mBAAmB;gBACrB,kBAAkB,OAAO;YAC3B;YAEA,IAAI,YAAY;gBACd,MAAM,kBAAkB;oBACtB;oBACA,OAAO,SAAS,MAAM;oBACtB;oBACA,WAAW,KAAK,GAAG,KAAK;oBACxB,uBAAuB,CAAC,KAAK,GAAG,KAAK,SAAS,IAAI;oBAClD,aAAa,WAAW,QAAQ;oBAChC,gBAAgB;wBACd,QAAQ,QAAQ,MAAM;wBACtB,OAAO,QAAQ,KAAK;wBACpB,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAClD;gBACF;gBAEA,WAAW,AAAC,YAAY,SAAS,MAAM,GAAI,KAAK,QAAQ,MAAM,EAAE;YAClE;YAEA,OAAO;QACT,SAAU;YACR,UAAU,OAAO;QACnB;IACF;IAEA,WAAW;IACX,MAAM,WAAW,SAAS,GAAG,CAAC,CAAC,SAAS,QAAU,eAAe,SAAS;IAC1E,MAAM,QAAQ,GAAG,CAAC;IAElB,OAAO;AACT;AAEA,cAAc;AACd,MAAM;IACI,QAAgB;IAChB,YAA+B,EAAE,CAAC;IAE1C,YAAY,OAAe,CAAE;QAC3B,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAM,UAAyB;QAC7B,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG;YACpB,IAAI,CAAC,OAAO;YACZ,OAAO,QAAQ,OAAO;QACxB;QAEA,OAAO,IAAI,QAAc,CAAC;YACxB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QACtB;IACF;IAEA,UAAgB;QACd,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,SAAS,CAAC,MAAM,GAAG,GAAG;YAC7B,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,KAAK;YACpC,IAAI,SAAS;gBACX,IAAI,CAAC,OAAO;gBACZ;YACF;QACF;IACF;AACF;AAGO,eAAe;IAKpB,IAAI;QACF,MAAM,aAAa,MAAM,YAAY;YACnC,cAAc;YACd,OAAO;QACT;QAEA,OAAO;YACL,SAAS,WAAW,OAAO;YAC3B,OAAO,WAAW,KAAK;YACvB,SAAS;gBACP,YAAY,WAAW,UAAU;gBACjC,YAAY,WAAW,UAAU;gBACjC,OAAO,WAAW,KAAK;gBACvB,gBAAgB,WAAW,cAAc;gBACzC,aAAa,WAAW,QAAQ;YAClC;QACF;IACF,EAAE,OAAO,OAAO;QACd,OAAO;YACL,SAAS;YACT,OAAO,CAAC,QAAQ,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;YACnE,SAAS;gBACP,aAAa,WAAW,QAAQ;YAClC;QACF;IACF;AACF;AAGO,SAAS;IACd,OAAO,WAAW,QAAQ;AAC5B;AAGO,SAAS;IACd,SAAS,OAAO,CAAC,CAAA;QACf,IAAI,YAAY,GAAG;QACnB,IAAI,QAAQ,GAAG;QACf,IAAI,aAAa,GAAG;IACtB;AACF;AAGO,IAAI,eAAe;IACxB,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;qBAIS,CAAC;IACpB;IAEA,eAAe;QACb,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;oBAIQ,CAAC;IACnB;IAEA,uBAAuB;QACrB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;kBAIM,CAAC;IACjB;IAEA,oBAAoB;QAClB,MAAM;QACN,aAAa;QACb,OAAO,CAAC;;;;eAIG,CAAC;IACd;IAEA,QAAQ;QACN,MAAM;QACN,aAAa;QACb,OAAO;IACT;AACF;AAGO,SAAS,gBAAgB,IAAY,EAAE,WAAmB,EAAE,KAAa;IAC9E,MAAM,MAAM,CAAC,OAAO,EAAE,KAAK,GAAG,IAAI;IAClC,eAAe;QACb,GAAG,YAAY;QACf,CAAC,IAAI,EAAE;YACL;YACA;YACA;QACF;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/file-manager.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\n\n// 文件管理工具类\nexport class FileManager {\n  private static instance: FileManager;\n  private baseDir: string;\n\n  private constructor() {\n    this.baseDir = process.cwd();\n  }\n\n  public static getInstance(): FileManager {\n    if (!FileManager.instance) {\n      FileManager.instance = new FileManager();\n    }\n    return FileManager.instance;\n  }\n\n  // 确保目录存在\n  public ensureDir(dirPath: string): void {\n    if (!fs.existsSync(dirPath)) {\n      fs.mkdirSync(dirPath, { recursive: true });\n    }\n  }\n\n  // 获取novels目录路径\n  public getNovelsDir(): string {\n    return path.join(this.baseDir, '..', 'novels');\n  }\n\n  // 获取chapters目录路径\n  public getChaptersDir(): string {\n    return path.join(this.baseDir, '..', 'chapters');\n  }\n\n  // 获取数据目录路径\n  public getDataDir(): string {\n    const dataDir = path.join(this.baseDir, 'data');\n    this.ensureDir(dataDir);\n    return dataDir;\n  }\n\n  // 获取改写结果目录路径\n  public getRewrittenDir(): string {\n    const rewrittenDir = path.join(this.getDataDir(), 'rewritten');\n    this.ensureDir(rewrittenDir);\n    return rewrittenDir;\n  }\n\n  // 获取特定小说的改写结果目录\n  public getNovelRewrittenDir(novelTitle: string): string {\n    const novelDir = path.join(this.getRewrittenDir(), this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 获取特定小说的章节目录\n  public getNovelChaptersDir(novelTitle: string): string {\n    const chaptersDir = this.getChaptersDir();\n    this.ensureDir(chaptersDir);\n    const novelDir = path.join(chaptersDir, this.sanitizeFilename(novelTitle));\n    this.ensureDir(novelDir);\n    return novelDir;\n  }\n\n  // 清理文件名中的非法字符\n  public sanitizeFilename(filename: string): string {\n    return filename.replace(/[<>:\"/\\\\|?*]/g, '_').trim();\n  }\n\n  // 读取文件内容\n  public readFile(filePath: string): string {\n    try {\n      return fs.readFileSync(filePath, 'utf-8');\n    } catch (error) {\n      console.error(`读取文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 写入文件内容\n  public writeFile(filePath: string, content: string): void {\n    try {\n      const dir = path.dirname(filePath);\n      this.ensureDir(dir);\n      fs.writeFileSync(filePath, content, 'utf-8');\n    } catch (error) {\n      console.error(`写入文件失败: ${filePath}`, error);\n      throw error;\n    }\n  }\n\n  // 检查文件是否存在\n  public fileExists(filePath: string): boolean {\n    return fs.existsSync(filePath);\n  }\n\n  // 获取目录中的所有文件\n  public listFiles(dirPath: string, extensions?: string[]): string[] {\n    try {\n      if (!fs.existsSync(dirPath)) {\n        return [];\n      }\n\n      const files = fs.readdirSync(dirPath);\n      \n      if (extensions) {\n        return files.filter(file => {\n          const ext = path.extname(file).toLowerCase();\n          return extensions.includes(ext);\n        });\n      }\n\n      return files;\n    } catch (error) {\n      console.error(`读取目录失败: ${dirPath}`, error);\n      return [];\n    }\n  }\n\n  // 获取文件信息\n  public getFileStats(filePath: string): fs.Stats | null {\n    try {\n      return fs.statSync(filePath);\n    } catch (error) {\n      console.error(`获取文件信息失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 删除文件\n  public deleteFile(filePath: string): boolean {\n    try {\n      if (fs.existsSync(filePath)) {\n        fs.unlinkSync(filePath);\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除文件失败: ${filePath}`, error);\n      return false;\n    }\n  }\n\n  // 删除目录\n  public deleteDir(dirPath: string): boolean {\n    try {\n      if (fs.existsSync(dirPath)) {\n        fs.rmSync(dirPath, { recursive: true, force: true });\n        return true;\n      }\n      return false;\n    } catch (error) {\n      console.error(`删除目录失败: ${dirPath}`, error);\n      return false;\n    }\n  }\n\n  // 复制文件\n  public copyFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.copyFileSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`复制文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 移动文件\n  public moveFile(srcPath: string, destPath: string): boolean {\n    try {\n      const destDir = path.dirname(destPath);\n      this.ensureDir(destDir);\n      fs.renameSync(srcPath, destPath);\n      return true;\n    } catch (error) {\n      console.error(`移动文件失败: ${srcPath} -> ${destPath}`, error);\n      return false;\n    }\n  }\n\n  // 获取目录大小\n  public getDirSize(dirPath: string): number {\n    let totalSize = 0;\n    \n    try {\n      if (!fs.existsSync(dirPath)) {\n        return 0;\n      }\n\n      const files = fs.readdirSync(dirPath);\n      \n      for (const file of files) {\n        const filePath = path.join(dirPath, file);\n        const stats = fs.statSync(filePath);\n        \n        if (stats.isDirectory()) {\n          totalSize += this.getDirSize(filePath);\n        } else {\n          totalSize += stats.size;\n        }\n      }\n    } catch (error) {\n      console.error(`计算目录大小失败: ${dirPath}`, error);\n    }\n\n    return totalSize;\n  }\n\n  // 格式化文件大小\n  public formatFileSize(bytes: number): string {\n    if (bytes === 0) return '0 B';\n    \n    const k = 1024;\n    const sizes = ['B', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    \n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  }\n\n  // 创建备份\n  public createBackup(filePath: string): string | null {\n    try {\n      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n      const ext = path.extname(filePath);\n      const baseName = path.basename(filePath, ext);\n      const dir = path.dirname(filePath);\n      \n      const backupPath = path.join(dir, `${baseName}_backup_${timestamp}${ext}`);\n      \n      if (this.copyFile(filePath, backupPath)) {\n        return backupPath;\n      }\n      \n      return null;\n    } catch (error) {\n      console.error(`创建备份失败: ${filePath}`, error);\n      return null;\n    }\n  }\n\n  // 清理旧备份文件\n  public cleanupBackups(dirPath: string, maxBackups: number = 5): void {\n    try {\n      const files = this.listFiles(dirPath);\n      const backupFiles = files\n        .filter(file => file.includes('_backup_'))\n        .map(file => ({\n          name: file,\n          path: path.join(dirPath, file),\n          stats: this.getFileStats(path.join(dirPath, file))\n        }))\n        .filter(item => item.stats !== null)\n        .sort((a, b) => b.stats!.mtime.getTime() - a.stats!.mtime.getTime());\n\n      // 删除超出数量限制的备份文件\n      if (backupFiles.length > maxBackups) {\n        const filesToDelete = backupFiles.slice(maxBackups);\n        for (const file of filesToDelete) {\n          this.deleteFile(file.path);\n        }\n      }\n    } catch (error) {\n      console.error(`清理备份文件失败: ${dirPath}`, error);\n    }\n  }\n}\n\n// 导出单例实例\nexport const fileManager = FileManager.getInstance();\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,MAAM;IACX,OAAe,SAAsB;IAC7B,QAAgB;IAExB,aAAsB;QACpB,IAAI,CAAC,OAAO,GAAG,QAAQ,GAAG;IAC5B;IAEA,OAAc,cAA2B;QACvC,IAAI,CAAC,YAAY,QAAQ,EAAE;YACzB,YAAY,QAAQ,GAAG,IAAI;QAC7B;QACA,OAAO,YAAY,QAAQ;IAC7B;IAEA,SAAS;IACF,UAAU,OAAe,EAAQ;QACtC,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;YAC3B,wGAAE,CAAC,SAAS,CAAC,SAAS;gBAAE,WAAW;YAAK;QAC1C;IACF;IAEA,eAAe;IACR,eAAuB;QAC5B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,iBAAiB;IACV,iBAAyB;QAC9B,OAAO,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM;IACvC;IAEA,WAAW;IACJ,aAAqB;QAC1B,MAAM,UAAU,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;QACxC,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,aAAa;IACN,kBAA0B;QAC/B,MAAM,eAAe,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;QAClD,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,gBAAgB;IACT,qBAAqB,UAAkB,EAAU;QACtD,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,gBAAgB,CAAC;QACzE,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,oBAAoB,UAAkB,EAAU;QACrD,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,SAAS,CAAC;QACf,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,gBAAgB,CAAC;QAC9D,IAAI,CAAC,SAAS,CAAC;QACf,OAAO;IACT;IAEA,cAAc;IACP,iBAAiB,QAAgB,EAAU;QAChD,OAAO,SAAS,OAAO,CAAC,iBAAiB,KAAK,IAAI;IACpD;IAEA,SAAS;IACF,SAAS,QAAgB,EAAU;QACxC,IAAI;YACF,OAAO,wGAAE,CAAC,YAAY,CAAC,UAAU;QACnC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,SAAS;IACF,UAAU,QAAgB,EAAE,OAAe,EAAQ;QACxD,IAAI;YACF,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,aAAa,CAAC,UAAU,SAAS;QACtC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,MAAM;QACR;IACF;IAEA,WAAW;IACJ,WAAW,QAAgB,EAAW;QAC3C,OAAO,wGAAE,CAAC,UAAU,CAAC;IACvB;IAEA,aAAa;IACN,UAAU,OAAe,EAAE,UAAqB,EAAY;QACjE,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO,EAAE;YACX;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,IAAI,YAAY;gBACd,OAAO,MAAM,MAAM,CAAC,CAAA;oBAClB,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC,MAAM,WAAW;oBAC1C,OAAO,WAAW,QAAQ,CAAC;gBAC7B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO,EAAE;QACX;IACF;IAEA,SAAS;IACF,aAAa,QAAgB,EAAmB;QACrD,IAAI;YACF,OAAO,wGAAE,CAAC,QAAQ,CAAC;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,UAAU,EAAE;YACvC,OAAO;QACT;IACF;IAEA,OAAO;IACA,WAAW,QAAgB,EAAW;QAC3C,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,WAAW;gBAC3B,wGAAE,CAAC,UAAU,CAAC;gBACd,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,OAAO;IACA,UAAU,OAAe,EAAW;QACzC,IAAI;YACF,IAAI,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC1B,wGAAE,CAAC,MAAM,CAAC,SAAS;oBAAE,WAAW;oBAAM,OAAO;gBAAK;gBAClD,OAAO;YACT;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,SAAS,EAAE;YACpC,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,YAAY,CAAC,SAAS;YACzB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,OAAO;IACA,SAAS,OAAe,EAAE,QAAgB,EAAW;QAC1D,IAAI;YACF,MAAM,UAAU,4GAAI,CAAC,OAAO,CAAC;YAC7B,IAAI,CAAC,SAAS,CAAC;YACf,wGAAE,CAAC,UAAU,CAAC,SAAS;YACvB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,QAAQ,IAAI,EAAE,UAAU,EAAE;YACnD,OAAO;QACT;IACF;IAEA,SAAS;IACF,WAAW,OAAe,EAAU;QACzC,IAAI,YAAY;QAEhB,IAAI;YACF,IAAI,CAAC,wGAAE,CAAC,UAAU,CAAC,UAAU;gBAC3B,OAAO;YACT;YAEA,MAAM,QAAQ,wGAAE,CAAC,WAAW,CAAC;YAE7B,KAAK,MAAM,QAAQ,MAAO;gBACxB,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,SAAS;gBACpC,MAAM,QAAQ,wGAAE,CAAC,QAAQ,CAAC;gBAE1B,IAAI,MAAM,WAAW,IAAI;oBACvB,aAAa,IAAI,CAAC,UAAU,CAAC;gBAC/B,OAAO;oBACL,aAAa,MAAM,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;QAEA,OAAO;IACT;IAEA,UAAU;IACH,eAAe,KAAa,EAAU;QAC3C,IAAI,UAAU,GAAG,OAAO;QAExB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAK;YAAM;YAAM;SAAK;QACrC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAEhD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO;IACA,aAAa,QAAgB,EAAiB;QACnD,IAAI;YACF,MAAM,YAAY,IAAI,OAAO,WAAW,GAAG,OAAO,CAAC,SAAS;YAC5D,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YACzB,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC,UAAU;YACzC,MAAM,MAAM,4GAAI,CAAC,OAAO,CAAC;YAEzB,MAAM,aAAa,4GAAI,CAAC,IAAI,CAAC,KAAK,GAAG,SAAS,QAAQ,EAAE,YAAY,KAAK;YAEzE,IAAI,IAAI,CAAC,QAAQ,CAAC,UAAU,aAAa;gBACvC,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,QAAQ,EAAE,UAAU,EAAE;YACrC,OAAO;QACT;IACF;IAEA,UAAU;IACH,eAAe,OAAe,EAAE,aAAqB,CAAC,EAAQ;QACnE,IAAI;YACF,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC;YAC7B,MAAM,cAAc,MACjB,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,CAAC,aAC7B,GAAG,CAAC,CAAA,OAAQ,CAAC;oBACZ,MAAM;oBACN,MAAM,4GAAI,CAAC,IAAI,CAAC,SAAS;oBACzB,OAAO,IAAI,CAAC,YAAY,CAAC,4GAAI,CAAC,IAAI,CAAC,SAAS;gBAC9C,CAAC,GACA,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,MAC9B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO,KAAK,EAAE,KAAK,CAAE,KAAK,CAAC,OAAO;YAEnE,gBAAgB;YAChB,IAAI,YAAY,MAAM,GAAG,YAAY;gBACnC,MAAM,gBAAgB,YAAY,KAAK,CAAC;gBACxC,KAAK,MAAM,QAAQ,cAAe;oBAChC,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,UAAU,EAAE,SAAS,EAAE;QACxC;IACF;AACF;AAGO,MAAM,cAAc,YAAY,WAAW", "debugId": null}}, {"offset": {"line": 1091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/lib/novel-parser.ts"], "sourcesContent": ["import fs from 'fs';\nimport path from 'path';\nimport { novelDb, chapterDb, Novel, Chapter } from './database';\nimport { fileManager } from './file-manager';\n\n// 小说解析配置\ninterface ParseConfig {\n  chapterPattern: RegExp;\n  minChapterLength: number;\n  maxChapterLength: number;\n}\n\n// 分卷/分集匹配模式（用于分栏，不作为章节）\nconst VOLUME_PATTERNS = [\n  /^\\s*(?:第[一二三四五六七八九十百千万\\d]+[卷集部])\\s*.*$/gmi,\n  /^\\s*(?:[卷集部][一二三四五六七八九十百千万\\d]+)\\s*.*$/gmi,\n];\n\n// 章节匹配模式\nconst CHAPTER_PATTERNS = [\n  /^\\s*(?:第[一二三四五六七八九十百千万\\d]+[章节回])\\s*.*$/gmi,\n  /^\\s*(?:Chapter\\s+\\d+)\\s*.*$/gmi,\n];\n\n// 解析小说文件\nexport async function parseNovelFile(filePath: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n}> {\n  const filename = path.basename(filePath);\n  const title = filename.replace(/\\.(txt|md)$/i, '');\n\n  // 读取文件内容\n  const content = fs.readFileSync(filePath, 'utf-8');\n\n  // 创建小说记录\n  const novel = novelDb.create({\n    title,\n    filename,\n  });\n\n  // 解析章节\n  const chapters = parseChapters(content, novel.id);\n\n  // 批量创建章节记录\n  const createdChapters = chapterDb.createBatch(chapters);\n\n  // 更新小说的章节数量\n  novelDb.update(novel.id, { chapterCount: createdChapters.length });\n\n  // 保存章节文件\n  await saveChapterFiles(createdChapters);\n\n  return {\n    novel: { ...novel, chapterCount: createdChapters.length },\n    chapters: createdChapters,\n  };\n}\n\n// 解析章节内容\nfunction parseChapters(content: string, novelId: string): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n\n  // 首先识别分卷/分集标记\n  const volumeMatches = findVolumeMarkers(content);\n\n  // 然后在每个分卷内或整个文本中查找章节\n  if (volumeMatches.length > 0) {\n    // 有分卷的情况\n    console.log(`Found ${volumeMatches.length} volumes`);\n    let chapterNumber = 1;\n\n    for (let i = 0; i < volumeMatches.length; i++) {\n      const volumeStart = volumeMatches[i].index;\n      const volumeEnd = i + 1 < volumeMatches.length ? volumeMatches[i + 1].index : content.length;\n      const volumeContent = content.slice(volumeStart, volumeEnd);\n\n      // 在分卷内查找章节\n      const volumeChapters = parseChaptersInVolume(volumeContent, novelId, chapterNumber, volumeMatches[i].title);\n      chapters.push(...volumeChapters);\n      chapterNumber += volumeChapters.length;\n    }\n  } else {\n    // 没有分卷，直接解析章节\n    const directChapters = parseChaptersInVolume(content, novelId, 1);\n    chapters.push(...directChapters);\n  }\n\n  console.log(`Successfully parsed ${chapters.length} chapters`);\n  return chapters;\n}\n\n// 查找分卷标记\nfunction findVolumeMarkers(content: string): Array<{ index: number; title: string }> {\n  const volumeMarkers: Array<{ index: number; title: string }> = [];\n\n  for (const pattern of VOLUME_PATTERNS) {\n    const matches = Array.from(content.matchAll(pattern));\n    for (const match of matches) {\n      volumeMarkers.push({\n        index: match.index!,\n        title: extractChapterTitle(match[0])\n      });\n    }\n  }\n\n  // 按位置排序\n  return volumeMarkers.sort((a, b) => a.index - b.index);\n}\n\n// 在指定内容中解析章节\nfunction parseChaptersInVolume(\n  content: string,\n  novelId: string,\n  startChapterNumber: number,\n  volumeTitle?: string\n): Omit<Chapter, 'id' | 'createdAt'>[] {\n  const chapters: Omit<Chapter, 'id' | 'createdAt'>[] = [];\n\n  // 尝试不同的章节匹配模式\n  let bestPattern: RegExp | null = null;\n  let bestMatches: RegExpMatchArray[] = [];\n\n  for (const pattern of CHAPTER_PATTERNS) {\n    const matches = Array.from(content.matchAll(pattern));\n    console.log(`Pattern ${pattern.source} found ${matches.length} matches in ${volumeTitle || 'content'}`);\n    if (matches.length > bestMatches.length) {\n      bestPattern = pattern;\n      bestMatches = matches;\n    }\n  }\n\n  if (!bestPattern || bestMatches.length === 0) {\n    // 如果没有找到章节标记，将整个内容作为一章（但要检查长度）\n    if (content.trim().length > 100) { // 只有内容足够长才作为章节\n      chapters.push({\n        novelId,\n        chapterNumber: startChapterNumber,\n        title: volumeTitle || '全文',\n        content: content.trim(),\n        filename: `chapter_${startChapterNumber}.txt`,\n      });\n    }\n    return chapters;\n  }\n\n  // 根据匹配结果分割章节\n  const chapterPositions = bestMatches.map((match, index) => ({\n    index: match.index!,\n    title: extractChapterTitle(match[0]),\n    chapterNumber: startChapterNumber + index,\n  }));\n\n  for (let i = 0; i < chapterPositions.length; i++) {\n    const currentPos = chapterPositions[i];\n    const nextPos = chapterPositions[i + 1];\n\n    const startIndex = currentPos.index;\n    const endIndex = nextPos ? nextPos.index : content.length;\n\n    const chapterContent = content.slice(startIndex, endIndex).trim();\n\n    if (chapterContent.length > 100) { // 提高最小长度要求\n      chapters.push({\n        novelId,\n        chapterNumber: currentPos.chapterNumber,\n        title: currentPos.title || `第${currentPos.chapterNumber}章`,\n        content: chapterContent,\n        filename: `chapter_${currentPos.chapterNumber}.txt`,\n      });\n    }\n  }\n\n  return chapters;\n}\n\n// 提取章节标题\nfunction extractChapterTitle(chapterText: string): string {\n  const lines = chapterText.split('\\n');\n  const firstLine = lines[0].trim();\n\n  // 如果第一行看起来像标题，使用它\n  if (firstLine.length < 100 && firstLine.length > 0) {\n    return firstLine;\n  }\n\n  // 否则尝试从前几行中找到标题\n  for (let i = 0; i < Math.min(3, lines.length); i++) {\n    const line = lines[i].trim();\n    if (line.length > 0 && line.length < 100) {\n      return line;\n    }\n  }\n\n  return '未命名章节';\n}\n\n// 保存章节文件到chapters目录\nasync function saveChapterFiles(chapters: Chapter[]): Promise<void> {\n  // 为每个小说创建子目录\n  const novelIds = [...new Set(chapters.map(ch => ch.novelId))];\n\n  for (const novelId of novelIds) {\n    const novel = novelDb.getById(novelId);\n    if (!novel) continue;\n\n    const novelDir = fileManager.getNovelChaptersDir(novel.title);\n\n    // 保存该小说的所有章节\n    const novelChapters = chapters.filter(ch => ch.novelId === novelId);\n    for (const chapter of novelChapters) {\n      const chapterPath = path.join(novelDir, chapter.filename);\n      fileManager.writeFile(chapterPath, chapter.content);\n    }\n  }\n}\n\n// 获取novels目录中的所有小说文件\nexport function getAvailableNovels(): string[] {\n  const novelsDir = fileManager.getNovelsDir();\n  return fileManager.listFiles(novelsDir, ['.txt', '.md']);\n}\n\n// 检查小说是否已经被解析\nexport function isNovelParsed(filename: string): boolean {\n  const novels = novelDb.getAll();\n  return novels.some(novel => novel.filename === filename);\n}\n\n// 重新解析小说（删除旧数据并重新解析）\nexport async function reparseNovel(filename: string): Promise<{\n  novel: Novel;\n  chapters: Chapter[];\n} | null> {\n  const novelsDir = fileManager.getNovelsDir();\n  const filePath = path.join(novelsDir, filename);\n\n  if (!fileManager.fileExists(filePath)) {\n    return null;\n  }\n\n  // 删除旧的小说和章节数据\n  const existingNovels = novelDb.getAll();\n  const existingNovel = existingNovels.find(novel => novel.filename === filename);\n\n  if (existingNovel) {\n    chapterDb.deleteByNovelId(existingNovel.id);\n    novelDb.delete(existingNovel.id);\n  }\n\n  // 重新解析\n  return await parseNovelFile(filePath);\n}\n\n// 解析章节范围字符串 (例如: \"1-5,7,10-12\")\nexport function parseChapterRange(rangeStr: string, maxChapter: number): number[] {\n  const chapters: number[] = [];\n  const parts = rangeStr.split(',').map(part => part.trim());\n\n  for (const part of parts) {\n    if (part.includes('-')) {\n      // 范围格式 (例如: \"1-5\")\n      const [start, end] = part.split('-').map(num => parseInt(num.trim()));\n      if (!isNaN(start) && !isNaN(end) && start <= end) {\n        for (let i = start; i <= Math.min(end, maxChapter); i++) {\n          if (i > 0 && !chapters.includes(i)) {\n            chapters.push(i);\n          }\n        }\n      }\n    } else {\n      // 单个章节\n      const chapterNum = parseInt(part);\n      if (!isNaN(chapterNum) && chapterNum > 0 && chapterNum <= maxChapter && !chapters.includes(chapterNum)) {\n        chapters.push(chapterNum);\n      }\n    }\n  }\n\n  return chapters.sort((a, b) => a - b);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;AACA;AACA;;;;;AASA,wBAAwB;AACxB,MAAM,kBAAkB;IACtB;IACA;CACD;AAED,SAAS;AACT,MAAM,mBAAmB;IACvB;IACA;CACD;AAGM,eAAe,eAAe,QAAgB;IAInD,MAAM,WAAW,4GAAI,CAAC,QAAQ,CAAC;IAC/B,MAAM,QAAQ,SAAS,OAAO,CAAC,gBAAgB;IAE/C,SAAS;IACT,MAAM,UAAU,wGAAE,CAAC,YAAY,CAAC,UAAU;IAE1C,SAAS;IACT,MAAM,QAAQ,mIAAO,CAAC,MAAM,CAAC;QAC3B;QACA;IACF;IAEA,OAAO;IACP,MAAM,WAAW,cAAc,SAAS,MAAM,EAAE;IAEhD,WAAW;IACX,MAAM,kBAAkB,qIAAS,CAAC,WAAW,CAAC;IAE9C,YAAY;IACZ,mIAAO,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE;QAAE,cAAc,gBAAgB,MAAM;IAAC;IAEhE,SAAS;IACT,MAAM,iBAAiB;IAEvB,OAAO;QACL,OAAO;YAAE,GAAG,KAAK;YAAE,cAAc,gBAAgB,MAAM;QAAC;QACxD,UAAU;IACZ;AACF;AAEA,SAAS;AACT,SAAS,cAAc,OAAe,EAAE,OAAe;IACrD,MAAM,WAAgD,EAAE;IAExD,cAAc;IACd,MAAM,gBAAgB,kBAAkB;IAExC,qBAAqB;IACrB,IAAI,cAAc,MAAM,GAAG,GAAG;QAC5B,SAAS;QACT,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,cAAc,MAAM,CAAC,QAAQ,CAAC;QACnD,IAAI,gBAAgB;QAEpB,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,MAAM,cAAc,aAAa,CAAC,EAAE,CAAC,KAAK;YAC1C,MAAM,YAAY,IAAI,IAAI,cAAc,MAAM,GAAG,aAAa,CAAC,IAAI,EAAE,CAAC,KAAK,GAAG,QAAQ,MAAM;YAC5F,MAAM,gBAAgB,QAAQ,KAAK,CAAC,aAAa;YAEjD,WAAW;YACX,MAAM,iBAAiB,sBAAsB,eAAe,SAAS,eAAe,aAAa,CAAC,EAAE,CAAC,KAAK;YAC1G,SAAS,IAAI,IAAI;YACjB,iBAAiB,eAAe,MAAM;QACxC;IACF,OAAO;QACL,cAAc;QACd,MAAM,iBAAiB,sBAAsB,SAAS,SAAS;QAC/D,SAAS,IAAI,IAAI;IACnB;IAEA,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,SAAS,MAAM,CAAC,SAAS,CAAC;IAC7D,OAAO;AACT;AAEA,SAAS;AACT,SAAS,kBAAkB,OAAe;IACxC,MAAM,gBAAyD,EAAE;IAEjE,KAAK,MAAM,WAAW,gBAAiB;QACrC,MAAM,UAAU,MAAM,IAAI,CAAC,QAAQ,QAAQ,CAAC;QAC5C,KAAK,MAAM,SAAS,QAAS;YAC3B,cAAc,IAAI,CAAC;gBACjB,OAAO,MAAM,KAAK;gBAClB,OAAO,oBAAoB,KAAK,CAAC,EAAE;YACrC;QACF;IACF;IAEA,QAAQ;IACR,OAAO,cAAc,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK;AACvD;AAEA,aAAa;AACb,SAAS,sBACP,OAAe,EACf,OAAe,EACf,kBAA0B,EAC1B,WAAoB;IAEpB,MAAM,WAAgD,EAAE;IAExD,cAAc;IACd,IAAI,cAA6B;IACjC,IAAI,cAAkC,EAAE;IAExC,KAAK,MAAM,WAAW,iBAAkB;QACtC,MAAM,UAAU,MAAM,IAAI,CAAC,QAAQ,QAAQ,CAAC;QAC5C,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,MAAM,CAAC,OAAO,EAAE,QAAQ,MAAM,CAAC,YAAY,EAAE,eAAe,WAAW;QACtG,IAAI,QAAQ,MAAM,GAAG,YAAY,MAAM,EAAE;YACvC,cAAc;YACd,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;QAC5C,+BAA+B;QAC/B,IAAI,QAAQ,IAAI,GAAG,MAAM,GAAG,KAAK;YAC/B,SAAS,IAAI,CAAC;gBACZ;gBACA,eAAe;gBACf,OAAO,eAAe;gBACtB,SAAS,QAAQ,IAAI;gBACrB,UAAU,CAAC,QAAQ,EAAE,mBAAmB,IAAI,CAAC;YAC/C;QACF;QACA,OAAO;IACT;IAEA,aAAa;IACb,MAAM,mBAAmB,YAAY,GAAG,CAAC,CAAC,OAAO,QAAU,CAAC;YAC1D,OAAO,MAAM,KAAK;YAClB,OAAO,oBAAoB,KAAK,CAAC,EAAE;YACnC,eAAe,qBAAqB;QACtC,CAAC;IAED,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;QAChD,MAAM,aAAa,gBAAgB,CAAC,EAAE;QACtC,MAAM,UAAU,gBAAgB,CAAC,IAAI,EAAE;QAEvC,MAAM,aAAa,WAAW,KAAK;QACnC,MAAM,WAAW,UAAU,QAAQ,KAAK,GAAG,QAAQ,MAAM;QAEzD,MAAM,iBAAiB,QAAQ,KAAK,CAAC,YAAY,UAAU,IAAI;QAE/D,IAAI,eAAe,MAAM,GAAG,KAAK;YAC/B,SAAS,IAAI,CAAC;gBACZ;gBACA,eAAe,WAAW,aAAa;gBACvC,OAAO,WAAW,KAAK,IAAI,CAAC,CAAC,EAAE,WAAW,aAAa,CAAC,CAAC,CAAC;gBAC1D,SAAS;gBACT,UAAU,CAAC,QAAQ,EAAE,WAAW,aAAa,CAAC,IAAI,CAAC;YACrD;QACF;IACF;IAEA,OAAO;AACT;AAEA,SAAS;AACT,SAAS,oBAAoB,WAAmB;IAC9C,MAAM,QAAQ,YAAY,KAAK,CAAC;IAChC,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,IAAI;IAE/B,kBAAkB;IAClB,IAAI,UAAU,MAAM,GAAG,OAAO,UAAU,MAAM,GAAG,GAAG;QAClD,OAAO;IACT;IAEA,gBAAgB;IAChB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,GAAG,CAAC,GAAG,MAAM,MAAM,GAAG,IAAK;QAClD,MAAM,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;QAC1B,IAAI,KAAK,MAAM,GAAG,KAAK,KAAK,MAAM,GAAG,KAAK;YACxC,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEA,oBAAoB;AACpB,eAAe,iBAAiB,QAAmB;IACjD,aAAa;IACb,MAAM,WAAW;WAAI,IAAI,IAAI,SAAS,GAAG,CAAC,CAAA,KAAM,GAAG,OAAO;KAAG;IAE7D,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO;QAEZ,MAAM,WAAW,8IAAW,CAAC,mBAAmB,CAAC,MAAM,KAAK;QAE5D,aAAa;QACb,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,KAAM,GAAG,OAAO,KAAK;QAC3D,KAAK,MAAM,WAAW,cAAe;YACnC,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,UAAU,QAAQ,QAAQ;YACxD,8IAAW,CAAC,SAAS,CAAC,aAAa,QAAQ,OAAO;QACpD;IACF;AACF;AAGO,SAAS;IACd,MAAM,YAAY,8IAAW,CAAC,YAAY;IAC1C,OAAO,8IAAW,CAAC,SAAS,CAAC,WAAW;QAAC;QAAQ;KAAM;AACzD;AAGO,SAAS,cAAc,QAAgB;IAC5C,MAAM,SAAS,mIAAO,CAAC,MAAM;IAC7B,OAAO,OAAO,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;AACjD;AAGO,eAAe,aAAa,QAAgB;IAIjD,MAAM,YAAY,8IAAW,CAAC,YAAY;IAC1C,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;IAEtC,IAAI,CAAC,8IAAW,CAAC,UAAU,CAAC,WAAW;QACrC,OAAO;IACT;IAEA,cAAc;IACd,MAAM,iBAAiB,mIAAO,CAAC,MAAM;IACrC,MAAM,gBAAgB,eAAe,IAAI,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAEtE,IAAI,eAAe;QACjB,qIAAS,CAAC,eAAe,CAAC,cAAc,EAAE;QAC1C,mIAAO,CAAC,MAAM,CAAC,cAAc,EAAE;IACjC;IAEA,OAAO;IACP,OAAO,MAAM,eAAe;AAC9B;AAGO,SAAS,kBAAkB,QAAgB,EAAE,UAAkB;IACpE,MAAM,WAAqB,EAAE;IAC7B,MAAM,QAAQ,SAAS,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,OAAQ,KAAK,IAAI;IAEvD,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,QAAQ,CAAC,MAAM;YACtB,mBAAmB;YACnB,MAAM,CAAC,OAAO,IAAI,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,MAAO,SAAS,IAAI,IAAI;YACjE,IAAI,CAAC,MAAM,UAAU,CAAC,MAAM,QAAQ,SAAS,KAAK;gBAChD,IAAK,IAAI,IAAI,OAAO,KAAK,KAAK,GAAG,CAAC,KAAK,aAAa,IAAK;oBACvD,IAAI,IAAI,KAAK,CAAC,SAAS,QAAQ,CAAC,IAAI;wBAClC,SAAS,IAAI,CAAC;oBAChB;gBACF;YACF;QACF,OAAO;YACL,OAAO;YACP,MAAM,aAAa,SAAS;YAC5B,IAAI,CAAC,MAAM,eAAe,aAAa,KAAK,cAAc,cAAc,CAAC,SAAS,QAAQ,CAAC,aAAa;gBACtG,SAAS,IAAI,CAAC;YAChB;QACF;IACF;IAEA,OAAO,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI;AACrC", "debugId": null}}, {"offset": {"line": 1332, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/cool/in-the-novel/novel-app/src/app/api/rewrite/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { chapterDb, jobDb, novelDb } from '@/lib/database';\nimport { rewriteChapters } from '@/lib/gemini';\nimport { parseChapterRange } from '@/lib/novel-parser';\nimport { fileManager } from '@/lib/file-manager';\nimport path from 'path';\n\n// POST - 创建改写任务\nexport async function POST(request: NextRequest) {\n  try {\n    const { novelId, chapterRange, rules } = await request.json();\n\n    if (!novelId || !chapterRange || !rules) {\n      return NextResponse.json(\n        { success: false, error: '参数不完整' },\n        { status: 400 }\n      );\n    }\n\n    // 获取小说信息\n    const novel = novelDb.getById(novelId);\n    if (!novel) {\n      return NextResponse.json(\n        { success: false, error: '小说不存在' },\n        { status: 404 }\n      );\n    }\n\n    // 获取所有章节\n    const allChapters = chapterDb.getByNovelId(novelId);\n    if (allChapters.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '该小说没有章节' },\n        { status: 404 }\n      );\n    }\n\n    // 解析章节范围\n    const chapterNumbers = parseChapterRange(chapterRange, allChapters.length);\n    if (chapterNumbers.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '无效的章节范围' },\n        { status: 400 }\n      );\n    }\n\n    // 获取要改写的章节\n    const chaptersToRewrite = allChapters.filter(chapter =>\n      chapterNumbers.includes(chapter.chapterNumber)\n    );\n\n    if (chaptersToRewrite.length === 0) {\n      return NextResponse.json(\n        { success: false, error: '没有找到指定的章节' },\n        { status: 404 }\n      );\n    }\n\n    // 创建改写任务\n    const job = jobDb.create({\n      novelId,\n      chapters: chapterNumbers,\n      ruleId: 'custom', // 暂时使用custom，后续可以关联到规则表\n      status: 'pending',\n      progress: 0,\n      details: {\n        totalChapters: chaptersToRewrite.length,\n        completedChapters: 0,\n        failedChapters: 0,\n        totalTokensUsed: 0,\n        totalProcessingTime: 0,\n        averageTimePerChapter: 0,\n        apiKeyStats: [],\n        chapterResults: [],\n        model: 'gemini-2.5-flash-lite',\n        concurrency: 3,\n      }\n    });\n\n    // 异步执行改写任务\n    executeRewriteJob(job.id, chaptersToRewrite, rules, novel.title);\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        jobId: job.id,\n        chaptersCount: chaptersToRewrite.length,\n        message: '改写任务已创建，正在处理中...',\n      },\n    });\n\n  } catch (error) {\n    console.error('创建改写任务失败:', error);\n    return NextResponse.json(\n      { success: false, error: '创建改写任务失败' },\n      { status: 500 }\n    );\n  }\n}\n\n// 异步执行改写任务 - 支持实时写入和详细进度\nasync function executeRewriteJob(\n  jobId: string,\n  chapters: any[],\n  rules: string,\n  novelTitle: string\n) {\n  const startTime = Date.now();\n\n  try {\n    // 更新任务状态为处理中\n    jobDb.update(jobId, {\n      status: 'processing',\n      progress: 0,\n      details: {\n        totalChapters: chapters.length,\n        completedChapters: 0,\n        failedChapters: 0,\n        totalTokensUsed: 0,\n        totalProcessingTime: 0,\n        averageTimePerChapter: 0,\n        apiKeyStats: jobDb.getById(jobId)?.details?.apiKeyStats || [],\n        chapterResults: [],\n        model: jobDb.getById(jobId)?.details?.model || 'gemini-2.5-flash-lite',\n        concurrency: jobDb.getById(jobId)?.details?.concurrency || 3,\n      }\n    });\n\n    // 准备章节数据\n    const chaptersData = chapters.map(chapter => ({\n      content: chapter.content,\n      title: chapter.title,\n      number: chapter.chapterNumber,\n    }));\n\n    // 创建输出目录\n    const outputDir = fileManager.getNovelRewrittenDir(novelTitle);\n\n    // 执行改写（降低并发数以避免429错误）\n    const results = await rewriteChapters(\n      chaptersData,\n      rules,\n      // 进度回调 - 更新详细信息\n      (progress, _currentChapter, details) => {\n        const currentJob = jobDb.getById(jobId);\n        if (currentJob && details) {\n          jobDb.update(jobId, {\n            progress: Math.round(progress),\n            details: {\n              totalChapters: currentJob.details?.totalChapters || chapters.length,\n              completedChapters: details.completed,\n              failedChapters: currentJob.details?.failedChapters || 0,\n              totalTokensUsed: details.totalTokensUsed,\n              totalProcessingTime: details.totalTime,\n              averageTimePerChapter: details.averageTimePerChapter,\n              apiKeyStats: details.apiKeyStats,\n              chapterResults: currentJob.details?.chapterResults || [],\n              model: currentJob.details?.model || 'gemini-2.5-flash-lite',\n              concurrency: currentJob.details?.concurrency || 3,\n            }\n          });\n        }\n      },\n      // 章节完成回调 - 实时写入\n      (chapterIndex, result) => {\n        if (result.success) {\n          // 立即写入完成的章节\n          const chapter = chapters[chapterIndex];\n          const filename = `chapter_${chapter.chapterNumber}_rewritten.txt`;\n          const filePath = path.join(outputDir, filename);\n          fileManager.writeFile(filePath, result.content);\n        }\n\n        // 更新章节结果到数据库\n        const currentJob = jobDb.getById(jobId);\n        if (currentJob?.details) {\n          const chapterResults = [...(currentJob.details.chapterResults || [])];\n          chapterResults[chapterIndex] = {\n            chapterNumber: result.details?.chapterNumber || chapterIndex + 1,\n            chapterTitle: result.details?.chapterTitle || `第${chapterIndex + 1}章`,\n            success: result.success,\n            error: result.error,\n            apiKeyUsed: result.details?.apiKeyUsed,\n            tokensUsed: result.details?.tokensUsed,\n            processingTime: result.details?.processingTime,\n            completedAt: new Date().toISOString(),\n          };\n\n          jobDb.update(jobId, {\n            details: {\n              ...currentJob.details,\n              chapterResults,\n              failedChapters: chapterResults.filter(r => !r.success).length,\n            }\n          });\n        }\n      },\n      3 // 并发数量\n    );\n\n    // 统计结果\n    let successCount = 0;\n    let totalTokensUsed = 0;\n    const resultSummary: any[] = [];\n\n    for (let i = 0; i < results.length; i++) {\n      const result = results[i];\n      const chapter = chapters[i];\n\n      if (result.success) {\n        successCount++;\n      }\n\n      if (result.details?.tokensUsed) {\n        totalTokensUsed += result.details.tokensUsed;\n      }\n\n      resultSummary.push({\n        chapterNumber: chapter.chapterNumber,\n        chapterTitle: chapter.title,\n        success: result.success,\n        error: result.error,\n        apiKeyUsed: result.details?.apiKeyUsed,\n        tokensUsed: result.details?.tokensUsed,\n        processingTime: result.details?.processingTime,\n      });\n    }\n\n    // 保存详细的结果摘要\n    const summaryPath = path.join(outputDir, 'rewrite_summary.json');\n    const totalProcessingTime = Date.now() - startTime;\n\n    const summaryData = JSON.stringify({\n      jobId,\n      novelTitle,\n      rules,\n      totalChapters: chapters.length,\n      successCount,\n      failedCount: chapters.length - successCount,\n      totalTokensUsed,\n      totalProcessingTime,\n      averageTimePerChapter: totalProcessingTime / chapters.length,\n      results: resultSummary,\n      completedAt: new Date().toISOString(),\n      model: 'gemini-2.5-flash-lite',\n      concurrency: 3,\n    }, null, 2);\n    fileManager.writeFile(summaryPath, summaryData);\n\n    // 更新任务状态为完成\n    const currentJob = jobDb.getById(jobId);\n    jobDb.update(jobId, {\n      status: 'completed',\n      progress: 100,\n      result: `成功改写 ${successCount}/${chapters.length} 章节，结果保存在: ${outputDir}`,\n      details: {\n        totalChapters: chapters.length,\n        completedChapters: successCount,\n        failedChapters: chapters.length - successCount,\n        totalTokensUsed,\n        totalProcessingTime,\n        averageTimePerChapter: totalProcessingTime / chapters.length,\n        apiKeyStats: currentJob?.details?.apiKeyStats || [],\n        chapterResults: resultSummary,\n        model: currentJob?.details?.model || 'gemini-2.5-flash-lite',\n        concurrency: currentJob?.details?.concurrency || 3,\n      }\n    });\n\n  } catch (error) {\n    console.error('执行改写任务失败:', error);\n    jobDb.update(jobId, {\n      status: 'failed',\n      result: `改写失败: ${error instanceof Error ? error.message : '未知错误'}`,\n    });\n  }\n}\n\n\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE3D,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC,OAAO;YACvC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,QAAQ,mIAAO,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,OAAO;YACV,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAQ,GACjC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,cAAc,qIAAS,CAAC,YAAY,CAAC;QAC3C,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,iBAAiB,IAAA,oJAAiB,EAAC,cAAc,YAAY,MAAM;QACzE,IAAI,eAAe,MAAM,KAAK,GAAG;YAC/B,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAU,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,WAAW;QACX,MAAM,oBAAoB,YAAY,MAAM,CAAC,CAAA,UAC3C,eAAe,QAAQ,CAAC,QAAQ,aAAa;QAG/C,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,OAAO,gJAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAY,GACrC;gBAAE,QAAQ;YAAI;QAElB;QAEA,SAAS;QACT,MAAM,MAAM,iIAAK,CAAC,MAAM,CAAC;YACvB;YACA,UAAU;YACV,QAAQ;YACR,QAAQ;YACR,UAAU;YACV,SAAS;gBACP,eAAe,kBAAkB,MAAM;gBACvC,mBAAmB;gBACnB,gBAAgB;gBAChB,iBAAiB;gBACjB,qBAAqB;gBACrB,uBAAuB;gBACvB,aAAa,EAAE;gBACf,gBAAgB,EAAE;gBAClB,OAAO;gBACP,aAAa;YACf;QACF;QAEA,WAAW;QACX,kBAAkB,IAAI,EAAE,EAAE,mBAAmB,OAAO,MAAM,KAAK;QAE/D,OAAO,gJAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ,OAAO,IAAI,EAAE;gBACb,eAAe,kBAAkB,MAAM;gBACvC,SAAS;YACX;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,OAAO,gJAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAW,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEA,yBAAyB;AACzB,eAAe,kBACb,KAAa,EACb,QAAe,EACf,KAAa,EACb,UAAkB;IAElB,MAAM,YAAY,KAAK,GAAG;IAE1B,IAAI;QACF,aAAa;QACb,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,UAAU;YACV,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,mBAAmB;gBACnB,gBAAgB;gBAChB,iBAAiB;gBACjB,qBAAqB;gBACrB,uBAAuB;gBACvB,aAAa,iIAAK,CAAC,OAAO,CAAC,QAAQ,SAAS,eAAe,EAAE;gBAC7D,gBAAgB,EAAE;gBAClB,OAAO,iIAAK,CAAC,OAAO,CAAC,QAAQ,SAAS,SAAS;gBAC/C,aAAa,iIAAK,CAAC,OAAO,CAAC,QAAQ,SAAS,eAAe;YAC7D;QACF;QAEA,SAAS;QACT,MAAM,eAAe,SAAS,GAAG,CAAC,CAAA,UAAW,CAAC;gBAC5C,SAAS,QAAQ,OAAO;gBACxB,OAAO,QAAQ,KAAK;gBACpB,QAAQ,QAAQ,aAAa;YAC/B,CAAC;QAED,SAAS;QACT,MAAM,YAAY,8IAAW,CAAC,oBAAoB,CAAC;QAEnD,sBAAsB;QACtB,MAAM,UAAU,MAAM,IAAA,yIAAe,EACnC,cACA,OACA,gBAAgB;QAChB,CAAC,UAAU,iBAAiB;YAC1B,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;YACjC,IAAI,cAAc,SAAS;gBACzB,iIAAK,CAAC,MAAM,CAAC,OAAO;oBAClB,UAAU,KAAK,KAAK,CAAC;oBACrB,SAAS;wBACP,eAAe,WAAW,OAAO,EAAE,iBAAiB,SAAS,MAAM;wBACnE,mBAAmB,QAAQ,SAAS;wBACpC,gBAAgB,WAAW,OAAO,EAAE,kBAAkB;wBACtD,iBAAiB,QAAQ,eAAe;wBACxC,qBAAqB,QAAQ,SAAS;wBACtC,uBAAuB,QAAQ,qBAAqB;wBACpD,aAAa,QAAQ,WAAW;wBAChC,gBAAgB,WAAW,OAAO,EAAE,kBAAkB,EAAE;wBACxD,OAAO,WAAW,OAAO,EAAE,SAAS;wBACpC,aAAa,WAAW,OAAO,EAAE,eAAe;oBAClD;gBACF;YACF;QACF,GACA,gBAAgB;QAChB,CAAC,cAAc;YACb,IAAI,OAAO,OAAO,EAAE;gBAClB,YAAY;gBACZ,MAAM,UAAU,QAAQ,CAAC,aAAa;gBACtC,MAAM,WAAW,CAAC,QAAQ,EAAE,QAAQ,aAAa,CAAC,cAAc,CAAC;gBACjE,MAAM,WAAW,4GAAI,CAAC,IAAI,CAAC,WAAW;gBACtC,8IAAW,CAAC,SAAS,CAAC,UAAU,OAAO,OAAO;YAChD;YAEA,aAAa;YACb,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;YACjC,IAAI,YAAY,SAAS;gBACvB,MAAM,iBAAiB;uBAAK,WAAW,OAAO,CAAC,cAAc,IAAI,EAAE;iBAAE;gBACrE,cAAc,CAAC,aAAa,GAAG;oBAC7B,eAAe,OAAO,OAAO,EAAE,iBAAiB,eAAe;oBAC/D,cAAc,OAAO,OAAO,EAAE,gBAAgB,CAAC,CAAC,EAAE,eAAe,EAAE,CAAC,CAAC;oBACrE,SAAS,OAAO,OAAO;oBACvB,OAAO,OAAO,KAAK;oBACnB,YAAY,OAAO,OAAO,EAAE;oBAC5B,YAAY,OAAO,OAAO,EAAE;oBAC5B,gBAAgB,OAAO,OAAO,EAAE;oBAChC,aAAa,IAAI,OAAO,WAAW;gBACrC;gBAEA,iIAAK,CAAC,MAAM,CAAC,OAAO;oBAClB,SAAS;wBACP,GAAG,WAAW,OAAO;wBACrB;wBACA,gBAAgB,eAAe,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,OAAO,EAAE,MAAM;oBAC/D;gBACF;YACF;QACF,GACA,EAAE,OAAO;;QAGX,OAAO;QACP,IAAI,eAAe;QACnB,IAAI,kBAAkB;QACtB,MAAM,gBAAuB,EAAE;QAE/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,MAAM,SAAS,OAAO,CAAC,EAAE;YACzB,MAAM,UAAU,QAAQ,CAAC,EAAE;YAE3B,IAAI,OAAO,OAAO,EAAE;gBAClB;YACF;YAEA,IAAI,OAAO,OAAO,EAAE,YAAY;gBAC9B,mBAAmB,OAAO,OAAO,CAAC,UAAU;YAC9C;YAEA,cAAc,IAAI,CAAC;gBACjB,eAAe,QAAQ,aAAa;gBACpC,cAAc,QAAQ,KAAK;gBAC3B,SAAS,OAAO,OAAO;gBACvB,OAAO,OAAO,KAAK;gBACnB,YAAY,OAAO,OAAO,EAAE;gBAC5B,YAAY,OAAO,OAAO,EAAE;gBAC5B,gBAAgB,OAAO,OAAO,EAAE;YAClC;QACF;QAEA,YAAY;QACZ,MAAM,cAAc,4GAAI,CAAC,IAAI,CAAC,WAAW;QACzC,MAAM,sBAAsB,KAAK,GAAG,KAAK;QAEzC,MAAM,cAAc,KAAK,SAAS,CAAC;YACjC;YACA;YACA;YACA,eAAe,SAAS,MAAM;YAC9B;YACA,aAAa,SAAS,MAAM,GAAG;YAC/B;YACA;YACA,uBAAuB,sBAAsB,SAAS,MAAM;YAC5D,SAAS;YACT,aAAa,IAAI,OAAO,WAAW;YACnC,OAAO;YACP,aAAa;QACf,GAAG,MAAM;QACT,8IAAW,CAAC,SAAS,CAAC,aAAa;QAEnC,YAAY;QACZ,MAAM,aAAa,iIAAK,CAAC,OAAO,CAAC;QACjC,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,UAAU;YACV,QAAQ,CAAC,KAAK,EAAE,aAAa,CAAC,EAAE,SAAS,MAAM,CAAC,WAAW,EAAE,WAAW;YACxE,SAAS;gBACP,eAAe,SAAS,MAAM;gBAC9B,mBAAmB;gBACnB,gBAAgB,SAAS,MAAM,GAAG;gBAClC;gBACA;gBACA,uBAAuB,sBAAsB,SAAS,MAAM;gBAC5D,aAAa,YAAY,SAAS,eAAe,EAAE;gBACnD,gBAAgB;gBAChB,OAAO,YAAY,SAAS,SAAS;gBACrC,aAAa,YAAY,SAAS,eAAe;YACnD;QACF;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,aAAa;QAC3B,iIAAK,CAAC,MAAM,CAAC,OAAO;YAClB,QAAQ;YACR,QAAQ,CAAC,MAAM,EAAE,iBAAiB,QAAQ,MAAM,OAAO,GAAG,QAAQ;QACpE;IACF;AACF", "debugId": null}}]}