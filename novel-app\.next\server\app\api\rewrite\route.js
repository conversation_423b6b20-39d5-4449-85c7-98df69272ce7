var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/rewrite/route.js")
R.c("server/chunks/node_modules_next_36431b2c._.js")
R.c("server/chunks/[root-of-the-server]__573e31fb._.js")
R.m("[project]/.next-internal/server/app/api/rewrite/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/rewrite/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/rewrite/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
