var R=require("../../../chunks/[turbopack]_runtime.js")("server/app/api/chapters/route.js")
R.c("server/chunks/node_modules_next_918f082c._.js")
R.c("server/chunks/[root-of-the-server]__7d7e9d8f._.js")
R.m("[project]/.next-internal/server/app/api/chapters/route/actions.js [app-rsc] (server actions loader, ecmascript)")
R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/chapters/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)")
module.exports=R.m("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/chapters/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)").exports
