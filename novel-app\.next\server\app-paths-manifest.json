{"/api/chapters/route": "app/api/chapters/route.js", "/api/gemini/reset/route": "app/api/gemini/reset/route.js", "/api/gemini/stats/route": "app/api/gemini/stats/route.js", "/api/jobs/route": "app/api/jobs/route.js", "/api/novels/route": "app/api/novels/route.js", "/api/presets/route": "app/api/presets/route.js", "/api/rewrite/route": "app/api/rewrite/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/page": "app/page.js"}